import { <PERSON><PERSON>roke<PERSON> } from "../interface/IBroker";
import {
  CancelOrderParams,
  Exchanges,
  HistoricalDataParams,
  ModifyOrderParams,
  PlaceOrderParams,
} from "../interface/params";
import {
  Holding,
  Instrument,
  MarketTiming,
  OHLC,
  OptionChain,
  Order,
  Position,
  Quote,
} from "../models";

/**
 * DemoOrderBroker - A demo broker implementation with fake order and position management.
 *
 * This broker provides:
 * - Fake order placement, modification, and cancellation
 * - Simulated position tracking based on filled orders
 * - Empty/mock implementations for all other methods
 *
 * Useful for testing, demos, and algorithm development without real broker integration.
 */
export class DemoOrderBroker implements IBroker {
  private orders: Order[] = [];
  private positions: Position[] = [];
  private orderIdCounter = 1;

  // Demo instruments - just a few popular stocks
  private demoInstruments: Instrument[] = [
    {
      instrumentToken: 738561,
      symbol: "RELIANCE",
      exchange: "NSE",
      name: "Reliance Industries Limited",
      segment: "EQ",
      lotSize: 1,
      tickSize: 0.05,
      instrumentType: "EQ",
    },
    {
      instrumentToken: 2953217,
      symbol: "TCS",
      exchange: "NSE",
      name: "Tata Consultancy Services Limited",
      segment: "EQ",
      lotSize: 1,
      tickSize: 0.05,
      instrumentType: "EQ",
    },
    {
      instrumentToken: 81153,
      symbol: "INFY",
      exchange: "NSE",
      name: "Infosys Limited",
      segment: "EQ",
      lotSize: 1,
      tickSize: 0.05,
      instrumentType: "EQ",
    },
  ];

  constructor() {
    console.log("[DemoOrderBroker] Initialized with fake order and position management");
  }

  // --- Auth Methods (Mock implementations) ---
  getLoginUrl(redirectUri?: string): string {
    return "https://demo-broker.com/login";
  }

  async generateSession(requestToken: string, apiSecret: string): Promise<void> {
    // Mock session generation
    console.log("[DemoOrderBroker] Mock session generated");
  }

  setAccessToken(accessToken: string): void {
    // Mock access token setting
    console.log("[DemoOrderBroker] Mock access token set");
  }

  // --- Account Methods (Mock implementations) ---
  async getProfile(): Promise<{ userId: string; userName: string; broker: string }> {
    return {
      userId: "DEMO001",
      userName: "Demo User",
      broker: "DemoOrderBroker",
    };
  }

  // --- Market Data Methods (Empty/Mock implementations) ---
  getInstruments(exchange?: Exchanges): Instrument[] {
    return this.demoInstruments;
  }

  async getLTP(symbols: string | string[]): Promise<Record<string, Quote>> {
    // Return empty quotes
    return {};
  }

  async getQuote(symbols: string | string[]): Promise<Record<string, Quote>> {
    // Return empty quotes
    return {};
  }

  async getOHLC(symbol: string | string[]): Promise<OHLC> {
    // Return mock OHLC data
    const symbolStr = Array.isArray(symbol) ? (symbol[0] || "UNKNOWN") : symbol;
    return {
      symbol: symbolStr,
      open: 100,
      high: 105,
      low: 95,
      close: 102,
      timestamp: new Date(),
    };
  }

  async getHistoricalData(params: HistoricalDataParams): Promise<OHLC[]> {
    // Return empty historical data
    return [];
  }

  getTiming(): MarketTiming {
    const now = new Date();
    const marketStart = new Date(now);
    marketStart.setHours(9, 15, 0, 0);
    const marketEnd = new Date(now);
    marketEnd.setHours(15, 30, 0, 0);

    return {
      marketOpen: "09:15",
      marketClose: "15:30",
      marketStartTime: marketStart,
      marketEndTime: marketEnd,
      timezone: "Asia/Calcutta",
      now,
    };
  }

  async getOptionChain(symbol: string, expiry: Date): Promise<OptionChain> {
    // Return empty option chain
    return {
      underlying: symbol,
      expiry,
      contracts: [],
    };
  }

  // --- Order Management Methods (FAKE IMPLEMENTATIONS) ---
  async placeOrder(params: PlaceOrderParams): Promise<Order> {
    const orderId = `DEMO${this.orderIdCounter++}`;
    const order: Order = {
      id: orderId,
      symbol: params.tradingsymbol,
      exchange: params.exchange,
      quantity: params.quantity,
      filledQuantity: params.quantity, // Simulate immediate fill
      price: params.price || 100, // Default price if not provided
      avgPrice: params.price || 100,
      orderType: params.orderType,
      product: params.product,
      status: "COMPLETE", // Simulate immediate execution
      transactionType: params.transactionType,
      placedAt: new Date(),
      updatedAt: new Date(),
    };

    this.orders.push(order);
    this.updatePositions(order);

    console.log(`[DemoOrderBroker] Order placed: ${order.transactionType} ${order.quantity} ${order.symbol} @ ${order.avgPrice}`);
    return order;
  }

  async modifyOrder(params: ModifyOrderParams): Promise<Order> {
    const order = this.orders.find(o => o.id === params.orderId);
    if (!order) {
      throw new Error(`Order ${params.orderId} not found`);
    }

    // Update order properties
    if (params.quantity !== undefined) order.quantity = params.quantity;
    if (params.price !== undefined) order.price = params.price;
    order.updatedAt = new Date();

    console.log(`[DemoOrderBroker] Order modified: ${order.id}`);
    return order;
  }

  async cancelOrder(params: CancelOrderParams): Promise<boolean> {
    const order = this.orders.find(o => o.id === params.orderId);
    if (!order) {
      return false;
    }

    order.status = "CANCELLED";
    order.updatedAt = new Date();

    console.log(`[DemoOrderBroker] Order cancelled: ${order.id}`);
    return true;
  }

  async getOrders(): Promise<Order[]> {
    return [...this.orders]; // Return copy of orders
  }

  async getOrderHistory(orderId: string): Promise<Order[]> {
    const order = this.orders.find(o => o.id === orderId);
    return order ? [order] : [];
  }

  // --- Position Management Methods (FAKE IMPLEMENTATIONS) ---
  async getPositions(): Promise<Position[]> {
    return [...this.positions]; // Return copy of positions
  }

  async getHoldings(): Promise<Holding[]> {
    // Return empty holdings
    return [];
  }

  // --- Ticker Methods (Mock implementations) ---
  async connectTicker(data: {
    onTicks: (ticks: Quote[]) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
    onError?: (err: any) => void;
    reconnect?: boolean | undefined;
  }): Promise<void> {
    // Mock ticker connection
    console.log("[DemoOrderBroker] Mock ticker connected");
    data.onConnect?.();
  }

  disconnectTicker(): void {
    console.log("[DemoOrderBroker] Mock ticker disconnected");
  }

  subscribeTicks(symbols: Array<number>, cb?: (ticks: Quote[]) => void): void {
    console.log("[DemoOrderBroker] Mock tick subscription for symbols:", symbols);
  }

  unsubscribeTicks(symbols: Array<number>): void {
    console.log("[DemoOrderBroker] Mock tick unsubscription for symbols:", symbols);
  }

  // --- Private Helper Methods ---
  private updatePositions(order: Order): void {
    if (order.status !== "COMPLETE") return;

    const existingPosition = this.positions.find(
      p => p.symbol === order.symbol && p.exchange === order.exchange && p.product === order.product
    );

    if (existingPosition) {
      // Update existing position
      const newQuantity = order.transactionType === "BUY"
        ? existingPosition.quantity + order.filledQuantity
        : existingPosition.quantity - order.filledQuantity;

      if (newQuantity === 0) {
        // Remove position if quantity becomes zero
        const index = this.positions.indexOf(existingPosition);
        this.positions.splice(index, 1);
      } else {
        // Update position quantity and average price
        const totalValue = (existingPosition.quantity * existingPosition.avgPrice) +
                          (order.filledQuantity * order.avgPrice * (order.transactionType === "BUY" ? 1 : -1));
        existingPosition.quantity = newQuantity;
        existingPosition.avgPrice = Math.abs(totalValue / newQuantity);
        existingPosition.pnl = 0; // Simplified P&L calculation
        existingPosition.dayPnl = 0;
      }
    } else if (order.transactionType === "BUY") {
      // Create new position for BUY orders
      const newPosition: Position = {
        symbol: order.symbol,
        exchange: order.exchange,
        quantity: order.filledQuantity,
        avgPrice: order.avgPrice,
        pnl: 0,
        dayPnl: 0,
        product: order.product,
      };
      this.positions.push(newPosition);
    }

    console.log(`[DemoOrderBroker] Position updated for ${order.symbol}: ${this.getPositionQuantity(order.symbol, order.exchange, order.product)} shares`);
  }

  private getPositionQuantity(symbol: string, exchange: string, product: string): number {
    const position = this.positions.find(
      p => p.symbol === symbol && p.exchange === exchange && p.product === product
    );
    return position ? position.quantity : 0;
  }

  // --- Public Helper Methods ---
  public getOrderCount(): number {
    return this.orders.length;
  }

  public getPositionCount(): number {
    return this.positions.length;
  }

  public clearOrders(): void {
    this.orders = [];
    console.log("[DemoOrderBroker] All orders cleared");
  }

  public clearPositions(): void {
    this.positions = [];
    console.log("[DemoOrderBroker] All positions cleared");
  }

  // --- Performance Analysis ---
  public getPerformance(): {
    totalOrders: number;
    completedOrders: number;
    cancelledOrders: number;
    totalPositions: number;
    realizedPnL: number;
    unrealizedPnL: number;
    totalPnL: number;
    grossTurnover: number;
    ordersBySymbol: Record<string, number>;
    positionsBySymbol: Record<string, { quantity: number; avgPrice: number; pnl: number }>;
  } {
    const completedOrders = this.orders.filter(o => o.status === "COMPLETE");
    const cancelledOrders = this.orders.filter(o => o.status === "CANCELLED");

    // Calculate realized P&L from completed trades
    let realizedPnL = 0;
    let grossTurnover = 0;

    // Group orders by symbol to calculate realized P&L
    const ordersBySymbol: Record<string, Order[]> = {};
    completedOrders.forEach(order => {
      const key = `${order.symbol}:${order.exchange}:${order.product}`;
      if (!ordersBySymbol[key]) {
        ordersBySymbol[key] = [];
      }
      ordersBySymbol[key].push(order);
      grossTurnover += order.filledQuantity * order.avgPrice;
    });

    // Calculate realized P&L using FIFO method
    Object.values(ordersBySymbol).forEach(orders => {
      const buyOrders = orders.filter(o => o.transactionType === "BUY").sort((a, b) => a.placedAt.getTime() - b.placedAt.getTime());
      const sellOrders = orders.filter(o => o.transactionType === "SELL").sort((a, b) => a.placedAt.getTime() - b.placedAt.getTime());

      let buyIndex = 0;
      let sellIndex = 0;
      let buyQuantityRemaining = buyOrders[0]?.filledQuantity || 0;
      let sellQuantityRemaining = sellOrders[0]?.filledQuantity || 0;

      while (buyIndex < buyOrders.length && sellIndex < sellOrders.length) {
        const buyOrder = buyOrders[buyIndex];
        const sellOrder = sellOrders[sellIndex];

        if (!buyOrder || !sellOrder) break;

        const matchedQuantity = Math.min(buyQuantityRemaining, sellQuantityRemaining);
        if (matchedQuantity > 0) {
          realizedPnL += matchedQuantity * (sellOrder.avgPrice - buyOrder.avgPrice);
        }

        buyQuantityRemaining -= matchedQuantity;
        sellQuantityRemaining -= matchedQuantity;

        if (buyQuantityRemaining === 0) {
          buyIndex++;
          buyQuantityRemaining = buyOrders[buyIndex]?.filledQuantity || 0;
        }

        if (sellQuantityRemaining === 0) {
          sellIndex++;
          sellQuantityRemaining = sellOrders[sellIndex]?.filledQuantity || 0;
        }
      }
    });

    // Calculate unrealized P&L from current positions
    const unrealizedPnL = this.positions.reduce((total, pos) => total + pos.pnl, 0);

    // Create order count by symbol
    const orderCountBySymbol: Record<string, number> = {};
    this.orders.forEach(order => {
      orderCountBySymbol[order.symbol] = (orderCountBySymbol[order.symbol] || 0) + 1;
    });

    // Create positions summary by symbol
    const positionsBySymbol: Record<string, { quantity: number; avgPrice: number; pnl: number }> = {};
    this.positions.forEach(pos => {
      positionsBySymbol[pos.symbol] = {
        quantity: pos.quantity,
        avgPrice: pos.avgPrice,
        pnl: pos.pnl
      };
    });

    return {
      totalOrders: this.orders.length,
      completedOrders: completedOrders.length,
      cancelledOrders: cancelledOrders.length,
      totalPositions: this.positions.length,
      realizedPnL: Math.round(realizedPnL * 100) / 100, // Round to 2 decimal places
      unrealizedPnL: Math.round(unrealizedPnL * 100) / 100,
      totalPnL: Math.round((realizedPnL + unrealizedPnL) * 100) / 100,
      grossTurnover: Math.round(grossTurnover * 100) / 100,
      ordersBySymbol: orderCountBySymbol,
      positionsBySymbol
    };
  }

  // --- Performance Reporting ---
  public printPerformanceReport(): void {
    const perf = this.getPerformance();

    console.log("\n=== DemoOrderBroker Performance Report ===");
    console.log(`Total Orders: ${perf.totalOrders}`);
    console.log(`  - Completed: ${perf.completedOrders}`);
    console.log(`  - Cancelled: ${perf.cancelledOrders}`);
    console.log(`Total Positions: ${perf.totalPositions}`);
    console.log(`Gross Turnover: ₹${perf.grossTurnover.toLocaleString()}`);
    console.log(`Realized P&L: ₹${perf.realizedPnL.toLocaleString()}`);
    console.log(`Unrealized P&L: ₹${perf.unrealizedPnL.toLocaleString()}`);
    console.log(`Total P&L: ₹${perf.totalPnL.toLocaleString()}`);

    if (Object.keys(perf.ordersBySymbol).length > 0) {
      console.log("\nOrders by Symbol:");
      Object.entries(perf.ordersBySymbol).forEach(([symbol, count]) => {
        console.log(`  ${symbol}: ${count} orders`);
      });
    }

    if (Object.keys(perf.positionsBySymbol).length > 0) {
      console.log("\nCurrent Positions:");
      Object.entries(perf.positionsBySymbol).forEach(([symbol, pos]) => {
        console.log(`  ${symbol}: ${pos.quantity} shares @ ₹${pos.avgPrice} (P&L: ₹${pos.pnl})`);
      });
    }
    console.log("==========================================\n");
  }
}