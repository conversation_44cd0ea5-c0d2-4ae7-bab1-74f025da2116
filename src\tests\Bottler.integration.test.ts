import { <PERSON><PERSON>Broker } from "../broker/ZerodhaBroker";
import { Blotter } from "../bottler"; // Assuming the new Blotter code is in this file
import { OHLC } from "../models"; // Assuming OHLC is in your models file
import { Interval } from "../interface";

describe("Blotter in Backtest Mode", () => {
  let broker: ZerodhaBroker;
  let blotter: Blotter;

  // Set a longer timeout for this test suite as it involves network requests for historical data
  jest.setTimeout(30000); 

  beforeAll(async () => {
    const { ZERODHA_API_KEY, ZERODHA_API_SECRET, ZERODHA_ACCESS_TOKEN } = process.env;

    // The test suite will be skipped if credentials are not available
    if (!ZERODHA_API_KEY || !ZERODHA_API_SECRET || !ZERODHA_ACCESS_TOKEN) {
      console.warn("⚠️ Missing Zerodha credentials, skipping Blotter backtest suite.");
      return;
    }

    broker = new ZerodhaBroker(
      ZERODHA_API_KEY,
      ZERODHA_API_SECRET, {
      accessToken: ZERODHA_ACCESS_TOKEN,
      debug: false
    });

    await broker.init();

    // --- KEY CHANGE: Initialize Blotter in 'backtest' mode ---
    blotter = new Blotter(broker, {
      instruments: ["NSE:RELIANCE", "NSE:TCS"], // Use actual instrument symbols
      intervals: ["5m", "15m"], // Let's test multiple intervals
      mode: "backtest", // Set the mode to backtest
      barLookbackWindow: 100,
      // 'preloadDays' is not necessary for backtesting mode
    });
  });

  // This test is still a valid check on the broker itself, so we can keep it.
  test("broker can fetch market timings correctly", async () => {
    if (!broker) return; // Skip if creds missing
    
    const timings = broker.getTiming();
    expect(timings.timezone).toBe("Asia/Calcutta");
  });

  // --- REWRITTEN TEST: This is the core backtest verification ---
  test("runs a full backtest and emits correct bar events", async () => {
    if (!blotter) {
      console.warn("Blotter not initialized, skipping test.");
      return;
    }

    // 1. Define a fixed, short historical period for the test
    const from = new Date("2025-11-01T09:15:00+05:30"); // Use a specific date
    const to = new Date("2023-11-01T15:30:00+05:30");   // A single trading day

    // 2. Setup collectors to store the events emitted by the blotter
    const collectedBars: { symbol: string; interval: Interval; bar: OHLC }[] = [];
    let backtestCompleted = false;

    // 3. Attach listeners BEFORE running the backtest
    blotter.on('bar', (symbol, interval, bar) => {
      console.log(`[Backtest] Received bar for ${symbol} at ${bar.timestamp.toISOString()}`);
      collectedBars.push({ symbol, interval, bar });
    });

    // 4. Run the backtest and wait for it to complete
    // We wrap this in a Promise that resolves when the 'backtestComplete' event is fired.
    await new Promise<void>((resolve) => {
        blotter.on('backtestComplete', () => {
            backtestCompleted = true;
            resolve();
        });

        // This triggers the entire simulation
        blotter.runBacktest(from, to);
    });

    // 5. Assert the results AFTER the simulation is finished
    expect(backtestCompleted).toBe(true);
    expect(collectedBars.length).toBeGreaterThan(0);

    // --- Assertions for specific results ---

    // Check that we got data for both instruments
    const symbolsInData = new Set(collectedBars.map(b => b.symbol));
    expect(symbolsInData).toContain("NSE:RELIANCE");
    expect(symbolsInData).toContain("NSE:TCS");

    // A standard Indian trading day has 75 5-minute bars (from 9:15 to 15:30)
    const reliance5mBars = collectedBars.filter(
        (b) => b.symbol === "NSE:RELIANCE" && b.interval === "5m"
    );
    expect(reliance5mBars.length).toBe(75);

    // A standard day has 25 15-minute bars
    const tcs15mBars = collectedBars.filter(
        (b) => b.symbol === "NSE:TCS" && b.interval === "15m"
    );
    expect(tcs15mBars.length).toBe(25);

    // Check the structure of a received bar
    const firstBarData = collectedBars[0];
    expect(firstBarData).toBeDefined(); // Ensure the first bar is defined
    expect(firstBarData!.bar).toHaveProperty("open");
    expect(firstBarData!.bar).toHaveProperty("high");
    expect(firstBarData!.bar).toHaveProperty("low");
    expect(firstBarData!.bar).toHaveProperty("close");
    expect(firstBarData!.bar.timestamp).toBeInstanceOf(Date);

    // Verify the data is within the correct date range
    expect(firstBarData!.bar!.timestamp.toISOString()).toContain("2023-11-01");
  });

  test("getBars returns the history after a backtest", async () => {
    // This test relies on the previous test having run and populated the bars.
    // In a more advanced setup, you might run a separate, smaller backtest here.
    if (!blotter) return;

    const reliance5mHistory = blotter.getBars("NSE:RELIANCE", "5m");
    expect(reliance5mHistory.length).toBe(75); // Should have the bars from the previous test run

    const tcs15mHistory = blotter.getBars("NSE:TCS", "15m");
    expect(tcs15mHistory.length).toBe(25);
  });
});