import { Algo, AlgoOptions } from "../algo";
import { IBroker, Interval } from "../interface";
import { OHLC, Order } from "../models";

/**
 * A predictable algorithm implementation for testing purposes.
 * - It records every event it receives.
 * - It has simple, deterministic trading logic.
 */
export class TestAlgo extends Algo {
  // Public properties to inspect after a test run
  public onStartCalled = false;
  public onEndCalled = false;
  public barsReceived: { symbol: string; interval: Interval; bar: OHLC }[] = [];
  public fillsReceived: Order[] = [];

  constructor(broker: IBroker, options: AlgoOptions) {
    super(broker, options);
  }

  async onStart(): Promise<void> {
    this.onStartCalled = true;
  }

  // Not used in this strategy
  onTick(): void {}

  onBar(symbol: string, interval: Interval, bar: OHLC): void {
    // Record that a bar was received
    this.barsReceived.push({ symbol, interval, bar });
    const barCount = this.barsReceived.filter(b => b.symbol === symbol).length;

    // --- Deterministic Trading Logic ---
    // On the 5th bar for this symbol, place a BUY order
    if (barCount === 5) {
      this.placeOrder({
        tradingsymbol: symbol,
        exchange: "NSE", // Example
        transactionType: "BUY",
        orderType: "MARKET",
        quantity: 10,
        product: "MIS",
      });
    }

    // On the 10th bar for this symbol, place a SELL order to close the position
    if (barCount === 10) {
      this.placeOrder({
        tradingsymbol: symbol,
        exchange: "NSE",
        transactionType: "SELL",
        orderType: "MARKET",
        quantity: 10,
        product: "MIS",
      });
    }
  }

  onFill(order: Order): void {
    // Record the simulated fill event
    this.fillsReceived.push(order);
  }

  async onEnd(): Promise<void> {
    this.onEndCalled = true;
  }
}