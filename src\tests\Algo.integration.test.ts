import { <PERSON>dhaBroker } from "../broker/ZerodhaBroker";
import { DemoOrderBroker } from "../broker/DemoOrderBroker";
import { AlgoOptions } from "../Algo";
import { TestAlgo } from "../strategy/TestAlgo"; // Import our test implementation

describe("Algo Class with BacktestBroker", () => {
  let realBroker: ZerodhaBroker;
  let backtestBroker: DemoOrderBroker;

  // Set a longer timeout for tests involving network requests
  jest.setTimeout(30000);

  beforeAll(async () => {
    const { ZERODHA_API_KEY, ZERODHA_API_SECRET, ZERODHA_ACCESS_TOKEN } = process.env;

    if (!ZERODHA_API_KEY || !ZERODHA_API_SECRET || !ZERODHA_ACCESS_TOKEN) {
      console.warn("⚠️ Missing Zerodha credentials, skipping Algo test suite.");
      return;
    }

    // 1. The Real Broker is always needed for fetching historical data
    realBroker = new ZerodhaBroker(ZERODHA_API_KEY, ZERODHA_API_SECRET, {
      accessToken: ZERODHA_ACCESS_TOKEN,
    });
    await realBroker.init();

    // 2. The Backtest Broker simulates trading and uses the real one for data
    backtestBroker = new DemoOrderBroker();
  });

  test("should run a full backtest, execute trades, and update positions", async () => {
    if (!backtestBroker) {
      console.warn("Brokers not initialized, skipping test.");
      return;
    }

    // --- ARRANGE ---
    const from = new Date("2025-09-01T09:15:00+05:30");
    const to = new Date("2025-09-01T15:30:00+05:30");

    const algoOptions: AlgoOptions = {
      instruments: ["NSE:RELIANCE"],
      intervals: ["5m"],
      mode: "backtest",
      barLookbackWindow: 50,
      backtestFrom: from,
      backtestTo: to,
      bottlerBroker: realBroker,
    };

    // Instantiate our test algorithm with the backtest broker
    const algo = new TestAlgo(backtestBroker, algoOptions);

    // --- ACT ---
    await algo.run();

    // --- ASSERT ---

    // 1. Assert Algo lifecycle methods were called
    expect(algo.onStartCalled).toBe(true);
    expect(algo.onEndCalled).toBe(true);

    // 2. Assert that the algo received the correct number of bars
    // A full Indian trading day has 75 5-minute bars
    expect(algo.barsReceived.length).toBe(75);
    expect(algo.barsReceived[0].symbol).toBe("RELIANCE");

    // 3. Assert that the trading logic was triggered and fills were received
    expect(algo.fillsReceived.length).toBe(2);

    const firstFill = algo.fillsReceived[0];
    expect(firstFill.transactionType).toBe("BUY");
    expect(firstFill.quantity).toBe(10);
    expect(firstFill.status).toBe("COMPLETE");
    expect(firstFill.symbol).toBe("RELIANCE");
    
    const secondFill = algo.fillsReceived[1];
    expect(secondFill.transactionType).toBe("SELL");
    expect(secondFill.quantity).toBe(10);

    // 4. Assert the state of the BacktestBroker
    const finalPositions = await backtestBroker.getPositions();
    // Position should be flat because we bought 10 and sold 10
    expect(finalPositions.length).toBe(0); 

    const performance = backtestBroker.getPerformance();
    expect(performance.totalTrades).toBe(2);
    // The final value should not be the initial cash, as P&L would have occurred
    expect(performance.finalPortfolioValue).not.toBe(100000); 

    // Calculate expected cash change
    const buyCost = firstFill.avgPrice * firstFill.quantity;
    const sellProceeds = secondFill.avgPrice * secondFill.quantity;
    const expectedFinalCash = 100000 - buyCost + sellProceeds;
    
    expect(performance.finalPortfolioValue).toBeCloseTo(expectedFinalCash);
  });

  test("should initialize in live mode without error", () => {
    if (!realBroker) {
        console.warn("Real broker not initialized, skipping test.");
        return;
    }

    // This is a smoke test to ensure the constructor works correctly with a live broker
    const algoOptions: AlgoOptions = {
        instruments: ["NSE:RELIANCE"],
        intervals: ["1m"],
        mode: "live",
    };

    // The test passes if the constructor doesn't throw an error
    expect(() => new TestAlgo(realBroker, algoOptions)).not.toThrow();
  });
});