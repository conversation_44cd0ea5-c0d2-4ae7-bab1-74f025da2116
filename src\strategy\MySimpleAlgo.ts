import { Algo, AlgoOptions } from "../algo";
import { simpleMovingAverage } from "../indicators";
import { IBroker, Interval } from "../interface";
import { OHLC, Order, Quote } from "../models";

export class MySimpleAlgo extends Algo {
  // Strategy-specific parameters
  private fastMA = 10;
  private slowMA = 20;

  constructor(broker: IBroker, options: AlgoOptions) {
    super(broker, options);
  }

  async onStart(): Promise<void> {
    console.log("[MySimpleAlgo] Starting up. Preloading data if configured.");
    // onStart is a great place to warm up indicators if needed,
    // but the blotter's preload handles the initial bar history.
  }

  // We won't use tick data in this bar-based strategy
  onTick(tick: Quote): void {}

  onBar(symbol: string, interval: Interval, bar: OHLC): void {
    // We only want to trade on our primary interval
    if (interval !== "5m") {
      return;
    }

    const bars = this.getBars(symbol, interval);

    // Wait until we have enough data to calculate indicators
    if (bars.length < this.slowMA) {
      return;
    }

    const closePrices = bars.map(b => b.close);
    const smaFast = simpleMovingAverage(closePrices, this.fastMA);
    const smaSlow = simpleMovingAverage(closePrices, this.slowMA);

    const lastFast = smaFast[smaFast.length - 1] || 0;
    const lastSlow = smaSlow[smaSlow.length - 1] || 0;
    const prevFast = smaFast[smaFast.length - 2] || 0;
    const prevSlow = smaSlow[smaSlow.length - 2] || 0;

    // --- Trading Logic ---
    const hasPosition = this.hasOpenPosition(symbol); // You'd implement this helper

    // Golden Cross: Fast MA crosses above Slow MA -> BUY SIGNAL
    if (lastFast > lastSlow && prevFast <= prevSlow && !hasPosition) {
      console.log(`[MySimpleAlgo] BUY SIGNAL on ${symbol} at price ${bar.close}`);
      this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE", // Example
          transactionType: "BUY",
          orderType: "MARKET",
          quantity: 1,
          product: "MIS"
      });
    }

    // Death Cross: Fast MA crosses below Slow MA -> SELL SIGNAL
    if (lastFast < lastSlow && prevFast >= prevSlow && hasPosition) {
      console.log(`[MySimpleAlgo] SELL SIGNAL on ${symbol} at price ${bar.close}`);
      this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE", // Example
          transactionType: "SELL",
          orderType: "MARKET",
          quantity: 1,
          product: "MIS"
      });
    }
  }

  onFill(order: Order): void {
    console.log(`[MySimpleAlgo] Order filled: ${order.transactionType} ${order.symbol} @ ${order.avgPrice}`);
  }

  async onEnd(): Promise<void> {
    console.log("[MySimpleAlgo] Backtest finished. Calculating results...");
    // Here you would calculate and print performance metrics like P&L, Sharpe ratio, etc.
  }

  private hasOpenPosition(symbol: string): boolean {
    // This is a simplified check. A real implementation would fetch from the broker
    // and manage state more carefully.
    // e.g., const positions = await this.getPositions();
    // return positions.some(p => p.symbol === symbol && p.quantity !== 0);
    return false; // Placeholder
  }
}